import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import { Button } from "@/components/UI/Button";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  BodyLarge,
  HeadingLarge,
  HeadingLarge2,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";
import Image from "next/image";
import React from "react";

type whyPeopleChooseCard = {
  icon: string;
  title: string;
  description: string;
};

type WhyPeopleChooseProps = {
  pill_Content: string;
  title: string;
  cards: whyPeopleChooseCard[];
};

const WhyPeopleChoose = ({
  pill_Content,
  title,
  cards,
}: WhyPeopleChooseProps) => {
  return (
    <SectionContainerLarge className="!mb-9 md:!mb-24 gap-4 md:gap-14 !px-0 ">
      <SectionContainerLarge className="!px-0 !mb-4 md:!mb-14">
        <Pill
          pill={pill_Content}
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-secondary-100"
          className="mb-2 md:mb-4"
        />
        <HeadingXLarge className="text-primary-800 text-center font-medium">
          {title}
        </HeadingXLarge>
      </SectionContainerLarge>
        {/* Desktop view */}
        <SectionContainerSmall className="md:flex flex-col  gap-8 hidden">
          {cards.map((card, index) => (
            <div
              key={index}
              className={`flex gap-10 ${
                index % 2 !== 0 ? "flex-row" : "flex-row-reverse"
              }`}
            >
              <div className="relative inline-block p-0.5 hover:cursor-pointer">
                <div className=" flex items-center justify-center w-[300px] h-[300px]">
                  <Image
                    src={
                      card.icon || "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a6f15-c699-7cad-aaf5-e5c95cbb0c82/Gemini_Generated_Image_rjjychrjjychrjjy 1.svg"
                    }
                    alt={card.title}
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
              <div className="flex flex-col justify-center max-w-[560px]">
                <HeadingLarge className="font-medium text-primary-800 mb-2">
                  {card.title}
                </HeadingLarge>
                {htmlParser(card.description, {
                  components: {
                    p: HeadingSmall,
                  },
                  classNames: {
                    p: "text-neutral-800 font-normal",
                  },
                })}
              </div>
            </div>
          ))}
        </SectionContainerSmall>

        {/* Mobile view */}
        <div className="block md:hidden ">
          <MobileCarousel totalSlides={cards.length}>
            {cards.map((card, index) => (
              <MobileCarouselItem key={index} className="flex justify-center">
                <div className="flex flex-col text-center items-center gap-4 border border-primary-300 rounded-xl p-4 h-full">
                  <div className="relative inline-block p-0.5">
                    <div className="w-[120px] h-[120px] flex items-center justify-center">
                      <img
                        src={
                          card.icon || "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a6f15-c699-7cad-aaf5-e5c95cbb0c82/Gemini_Generated_Image_rjjychrjjychrjjy 1.svg"
                        }
                        alt={card.title}
                      />
                    </div>
                  </div>

                  <div className="w-full max-w-[305px] mt-4">
                    <BodyLarge className="font-bold text-neutal-1100 mb-2">
                      {card.title}
                    </BodyLarge>
                    {htmlParser(card.description, {
                      classNames: {
                        p: "text-neutral-800 font-normal text-sm w-[300px] mx-auto",
                      },
                    })}
                  </div>
                </div>
              </MobileCarouselItem>
            ))}
          </MobileCarousel>
        </div>
    </SectionContainerLarge>
  );
};

export default WhyPeopleChoose;
